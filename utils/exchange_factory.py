#!/usr/bin/env python3
"""
交易所工厂模块 - 简化交易所实例化
提供统一的交易所创建接口，支持多种交易所配置
"""

import os
import logging
import ccxt.pro as ccxt
from typing import Tuple, Optional


class ExchangeFactory:
    """交易所工厂类 - 负责创建和配置交易所实例"""
    
    def __init__(self):
        self.trading_mode = os.environ.get("TRADING_MODE", "sandbox")
        self.is_sandbox = self.trading_mode == "sandbox"
        
        logging.info(f"交易模式: {'模拟盘' if self.is_sandbox else '实盘'}")
        if not self.is_sandbox:
            logging.warning("🚨 警告：当前为实盘模式，将使用真实资金进行交易！")
    
    def create_bitget(self) -> Optional[ccxt.Exchange]:
        """创建Bitget交易所实例"""
        api_key = os.environ.get("api_key_bitget")
        secret_key = os.environ.get("secret_key_bitget")
        passphrase = os.environ.get("passphrase_bitget")
        
        if not all([api_key, secret_key, passphrase]):
            logging.error("缺少Bitget交易所的API密钥环境变量")
            return None
        
        exchange = ccxt.bitget({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'swap',  # 永续合约
            }
        })
        
        exchange.set_sandbox_mode(self.is_sandbox)
        logging.info(f"✅ 成功创建Bitget交易所实例 ({'沙盒模式' if self.is_sandbox else '实盘模式'})")
        return exchange
    
    def create_binance(self) -> Optional[ccxt.Exchange]:
        """创建Binance交易所实例"""
        api_key = os.environ.get("api_key_binance")
        secret_key = os.environ.get("secret_key_binance")
        
        if not all([api_key, secret_key]):
            logging.error("缺少Binance交易所的API密钥环境变量")
            return None
        
        exchange = ccxt.binance({
            'apiKey': api_key,
            'secret': secret_key,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'swap',  # 永续合约
            }
        })
        
        exchange.set_sandbox_mode(self.is_sandbox)
        logging.info(f"✅ 成功创建Binance交易所实例 ({'沙盒模式' if self.is_sandbox else '实盘模式'})")
        return exchange
    
    def create_okx(self) -> Optional[ccxt.Exchange]:
        """创建OKX交易所实例"""
        api_key = os.environ.get("api_key_okx")
        secret_key = os.environ.get("secret_key_okx")

        
        if not all([api_key, secret_key]):
            logging.error("缺少OKX交易所的API密钥环境变量")
            return None
        
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'swap',  # 永续合约
            }
        })
        
        exchange.set_sandbox_mode(self.is_sandbox)
        logging.info(f"✅ 成功创建OKX交易所实例 ({'沙盒模式' if self.is_sandbox else '实盘模式'})")
        return exchange
    
    def create_bybit(self) -> Optional[ccxt.Exchange]:
        """创建Bybit交易所实例"""
        api_key = os.environ.get("api_key_bybit")
        secret_key = os.environ.get("secret_key_bybit")
        
        if not all([api_key, secret_key]):
            logging.error("缺少Bybit交易所的API密钥环境变量")
            return None
        
        exchange = ccxt.bybit({
            'apiKey': api_key,
            'secret': secret_key,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'swap',  # 永续合约
            }
        })
        
        exchange.set_sandbox_mode(self.is_sandbox)
        logging.info(f"✅ 成功创建Bybit交易所实例 ({'沙盒模式' if self.is_sandbox else '实盘模式'})")
        return exchange
    
    def create_exchange_pair(self, exchange1_name: str = 'bitget', exchange2_name: str = 'binance') -> Tuple[Optional[ccxt.Exchange], Optional[ccxt.Exchange]]:
        """
        创建交易所对（自动验证凭证）

        Args:
            exchange1_name: 第一个交易所名称
            exchange2_name: 第二个交易所名称

        Returns:
            Tuple[Optional[ccxt.Exchange], Optional[ccxt.Exchange]]: 交易所实例对，失败时返回 (None, None)
        """
        # 支持的交易所配置
        exchange_config = {
            'bitget': {
                'creator': self.create_bitget,
                'credentials': ['api_key_bitget', 'secret_key_bitget', 'passphrase_bitget']
            },
            'binance': {
                'creator': self.create_binance,
                'credentials': ['api_key_binance', 'secret_key_binance']
            },
            'okx': {
                'creator': self.create_okx,
                'credentials': ['api_key_okx', 'secret_key_okx']
            },
            'bybit': {
                'creator': self.create_bybit,
                'credentials': ['api_key_bybit', 'secret_key_bybit']
            }
        }

        # 验证交易所名称
        if exchange1_name not in exchange_config:
            logging.error(f"不支持的交易所: {exchange1_name}")
            return None, None

        if exchange2_name not in exchange_config:
            logging.error(f"不支持的交易所: {exchange2_name}")
            return None, None

        # 验证凭证
        exchange_names = [exchange1_name, exchange2_name]
        missing_credentials = []

        for exchange_name in exchange_names:
            config = exchange_config[exchange_name]
            credentials = config['credentials']

            # 检查所有必需的凭证是否存在
            missing_creds = [cred for cred in credentials if not os.environ.get(cred)]
            if missing_creds:
                missing_credentials.append(f"{exchange_name}({', '.join(missing_creds)})")

        if missing_credentials:
            logging.error(f"以下交易所缺少凭证: {missing_credentials}")
            return None, None

        # 创建交易所实例
        logging.info(f"创建交易所对: {exchange1_name} + {exchange2_name}")

        exchange1 = exchange_config[exchange1_name]['creator']()
        exchange2 = exchange_config[exchange2_name]['creator']()

        if exchange1 is None or exchange2 is None:
            logging.error("交易所实例创建失败")
            return None, None

        logging.info(f"✅ 成功创建交易所对: {exchange1.id} + {exchange2.id}")
        return exchange1, exchange2



# 全局工厂实例
exchange_factory = ExchangeFactory()



def create_exchange_pair_safe(exchange1_name: str = 'bitget', exchange2_name: str = 'binance') -> Tuple[Optional[ccxt.Exchange], Optional[ccxt.Exchange]]:
    """
    安全创建交易所对（推荐使用）
    自动验证凭证并创建实例，一步到位

    Args:
        exchange1_name: 第一个交易所名称
        exchange2_name: 第二个交易所名称

    Returns:
        Tuple[Optional[ccxt.Exchange], Optional[ccxt.Exchange]]: 交易所实例对
    """
    return exchange_factory.create_exchange_pair(exchange1_name, exchange2_name)


