import asyncio
import ccxt.pro as ccxt
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json
import os


class SymbolAnalyzer:
    """币种价差分析器 - 分析两个交易所共同币种的价差波动"""
    
    def __init__(self, exchange1: ccxt.Exchange, exchange2: ccxt.Exchange,
                 analysis_duration_hours: int = 24,top_symbols_count: int = 20):
        self.ex1 = exchange1
        self.ex2 = exchange2
        self.analysis_duration_hours = analysis_duration_hours
        self.top_symbols_count = top_symbols_count


        self.common_symbols: List[str] = []
        self.price_data: Dict[str, List[Dict]] = {}
        self.analysis_results: Dict[str, Dict] = {}


        self.data_file = "symbol_analysis_data.json"
        self.recommended_symbols_file = "top_symbols.json"


    
    async def _initialize(self):
        """初始化分析器，获取共同币种"""
        try:
            logging.info("正在加载交易所市场数据...")
            await self.ex1.load_markets()
            await self.ex2.load_markets()
            
            # 获取两个交易所的永续合约币种
            ex1_symbols = set()
            ex2_symbols = set()
            
            for symbol, market in self.ex1.markets.items():
                if market.get('type') == 'swap' and market.get('active', True):
                    ex1_symbols.add(symbol)
            
            for symbol, market in self.ex2.markets.items():
                if market.get('type') == 'swap' and market.get('active', True):
                    ex2_symbols.add(symbol)
            
            # 找到共同币种
            self.common_symbols = list(ex1_symbols.intersection(ex2_symbols))
            
            # 过滤掉一些不适合套利的币种
            filtered_symbols = []
            for symbol in self.common_symbols:
                try:
                    # 检查最小交易量等限制
                    market1 = self.ex1.markets[symbol]
                    market2 = self.ex2.markets[symbol]
                    
                    min_amount1 = market1.get('limits', {}).get('amount', {}).get('min', 0)
                    min_amount2 = market2.get('limits', {}).get('amount', {}).get('min', 0)
                    
                    if min_amount1 and min_amount2 and min_amount1 > 0 and min_amount2 > 0:
                        filtered_symbols.append(symbol)
                        
                except Exception as e:
                    logging.warning(f"过滤币种 {symbol} 时出错: {str(e)}")
                    continue
            
            self.common_symbols = filtered_symbols
            logging.info(f"发现 {len(self.common_symbols)} 个共同币种: {self.common_symbols[:10]}...")
            
            # 清空历史数据，确保每次都使用最新数据
            self._clear_data_file()
            
        except Exception as e:
            logging.error(f"初始化分析器失败: {type(e).__name__}: {str(e)}")
            raise

    async def run(self):
        """运行分析器"""
        try:
            # 初始化
            await self._initialize()

            # 获取历史数据并分析
            logging.info(f"开始获取过去 {self.analysis_duration_hours} 小时的历史数据...")
            success = await self._collect_historical_data(hours_back=self.analysis_duration_hours)

            if not success:
                logging.error("历史数据获取失败")
                return None

            logging.info("分析数据...")
            self._analyze_symbols()

            if self.analysis_results:
                self._print_analysis_report(top_count=self.top_symbols_count)

                # 显示推荐币种
                top_symbols = self._get_top_symbols(count=self.top_symbols_count)

                # 保存结果到文件
                # 获取时间戳
                timestamp = None
                if top_symbols and top_symbols[0] in self.analysis_results:
                    timestamp = self.analysis_results[top_symbols[0]].get('analysis_timestamp')
                else:
                    logging.warning("无法获取分析结果时间戳")

                # 构建保存数据
                save_recommend_symbols_data = {
                    'timestamp': timestamp,
                    'top_symbols': top_symbols,
                    'analysis_results': {symbol: self.analysis_results[symbol] for symbol in top_symbols if
                                         symbol in self.analysis_results},
                    'data_source': 'historical',
                    'data_period_hours': self.analysis_duration_hours
                }

                # 保存到文件
                with open(self.recommended_symbols_file, 'w', encoding='utf-8') as f:
                    json.dump(save_recommend_symbols_data, f, indent=2, ensure_ascii=False)

                logging.info(f"\n✅ 推荐结果已保存到 {self.recommended_symbols_file}")
                return save_recommend_symbols_data
            else:
                logging.warning("分析结果为空")
                return None

        except Exception as e:
            logging.error(f"程序异常: {type(e).__name__}: {str(e)}")
            raise

    def _clear_data_file(self):
        """清空数据文件和内存数据，确保每次都使用最新数据"""
        try:
            # 初始化空的数据结构
            self.price_data = {}
            self.analysis_results = {}

            # 清空数据文件（如果存在）
            if os.path.exists(self.data_file):
                empty_data = {
                    'timestamp': datetime.now().isoformat(),
                    'price_data': {},
                    'analysis_results': {},
                    'note': 'Data cleared for fresh analysis - will be populated with new data'
                }
                with open(self.data_file, 'w') as f:
                    json.dump(empty_data, f, indent=2)
                logging.info(f"已清空历史数据文件: {self.data_file}")
            else:
                logging.info("数据文件不存在，将创建新文件")

        except Exception as e:
            logging.warning(f"清空数据文件失败: {str(e)}")
            # 确保内存中的数据是空的
            self.price_data = {}
            self.analysis_results = {}

    def _save_price_and_analysis_data(self):
        """保存分析结果和历史价格数据到文件"""
        try:
            # 保存分析结果和历史价格数据
            data = {
                'price_data': self.price_data,
                'analysis_results': self.analysis_results,
                'last_update': datetime.now().isoformat(),
                'note': 'Analysis results and historical price data saved (cleared on each initialization)'
            }
            with open(self.data_file, 'w') as f:
                json.dump(data, f, indent=2)
            logging.debug(f"分析结果和历史数据已保存到 {self.data_file}")
        except Exception as e:
            logging.error(f"保存数据失败: {str(e)}")
    
    async def _collect_historical_data(self, hours_back: int = 24, timeframe: str = '1m'):
        """从交易所获取历史数据进行分析"""
        logging.info(f"开始获取 {len(self.common_symbols)} 个币种的历史数据（过去{hours_back}小时）")

        # 计算时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours_back)

        # 转换为毫秒时间戳
        since = int(start_time.timestamp() * 1000)

        successful_symbols = 0

        try:
            # 并发获取所有币种的历史数据
            tasks = []
            for symbol in self.common_symbols:
                tasks.append(self._fetch_symbol_historical_data(symbol, since, timeframe))

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            for i, result in enumerate(results):
                symbol = self.common_symbols[i]
                if isinstance(result, list) and result:
                    if symbol not in self.price_data:
                        self.price_data[symbol] = []

                    # 添加历史数据
                    self.price_data[symbol].extend(result)

                    # # 按时间戳排序并去重（获取历史数据时已完成去重）
                    # self.price_data[symbol] = self._deduplicate_data(self.price_data[symbol])

                    successful_symbols += 1
                    # logging.info(f"✓ {symbol}: 获取到 {len(result)} 个有效数据点")

                elif isinstance(result, Exception):
                    logging.warning(f"✗ {symbol}: {str(result)}")
                else:
                    logging.warning(f"✗ {symbol}: 无数据")

            # 保存历史价格数据
            self._save_price_and_analysis_data()

            logging.info(f"历史数据获取完成: {successful_symbols}/{len(self.common_symbols)} 个币种成功")
            return successful_symbols > 0

        except Exception as e:
            logging.error(f"获取历史数据失败: {type(e).__name__}: {str(e)}")
            return False

    async def _fetch_symbol_historical_data(self, symbol: str, since: int, timeframe: str = '1m'):
        """获取单个币种的历史数据（支持分批获取）"""
        try:
            # 分批获取历史K线数据（并发执行）
            task1 = asyncio.create_task(self._fetch_ohlcv_in_batches(self.ex1, symbol, timeframe, since))
            task2 = asyncio.create_task(self._fetch_ohlcv_in_batches(self.ex2, symbol, timeframe, since))
            ohlcv1, ohlcv2 = await asyncio.gather(task1, task2)

            if not ohlcv1 or not ohlcv2:
                logging.warning(f"获取 {symbol} 历史数据失败: ex1={len(ohlcv1) if ohlcv1 else 0}, ex2={len(ohlcv2) if ohlcv2 else 0}")
                return []

            # 基于时间戳精确对齐两个交易所的数据
            aligned_data = self._align_ohlcv_data(ohlcv1, ohlcv2, symbol)
            if not aligned_data:
                logging.warning(f"获取 {symbol} 历史数据失败: 无法对齐时间戳")
                return []
            historical_data = []

            # 处理每个时间点的对齐数据
            for data_pair in aligned_data:
                timestamp1, open1, high1, low1, close1, volume1 = data_pair['ex1']
                timestamp2, open2, high2, low2, close2, volume2 = data_pair['ex2']

                # 使用收盘价作为参考价格
                ex1_price = close1
                ex2_price = close2

                #价差
                price_diff = abs(ex1_price - ex2_price)

                # 直接计算两个交易所的价格差异
                spread=price_diff/min(ex1_price,ex2_price)*100


                data_point = {
                    'timestamp': datetime.fromtimestamp(timestamp1 / 1000).isoformat(),
                    'symbol': symbol,
                    'ex1_price': ex1_price,
                    'ex2_price': ex2_price,
                    'price_diff': round(price_diff,4),
                    'spread': round(spread,4),
                    'ex1_volume': volume1,
                    'ex2_volume': volume2
                }

                historical_data.append(data_point)

            return historical_data

        except Exception as e:
            logging.debug(f"获取 {symbol} 历史数据失败: {str(e)}")
            return []

    def _align_ohlcv_data(self, ohlcv1: List, ohlcv2: List, symbol: str) -> List[Dict]:
        """
        基于时间戳精确对齐两个交易所的OHLCV数据

        Args:
            ohlcv1: 交易所1的OHLCV数据
            ohlcv2: 交易所2的OHLCV数据
            symbol: 交易对符号


        Returns:
            对齐后的数据列表，每个元素包含{'ex1': ohlcv_data, 'ex2': ohlcv_data}
        """
        if not ohlcv1 or not ohlcv2:
            return []

        # 将数据转换为字典格式，以时间戳为键
        data1_dict = {int(item[0]): item for item in ohlcv1}
        data2_dict = {int(item[0]): item for item in ohlcv2}

        aligned_data = []

        # 获取所有时间戳并排序
        all_timestamps = sorted(set(data1_dict.keys()) | set(data2_dict.keys()))

        for timestamp in all_timestamps:
            # 尝试精确匹配
            if timestamp in data1_dict and timestamp in data2_dict:
                aligned_data.append({
                    'ex1': data1_dict[timestamp],
                    'ex2': data2_dict[timestamp]
                })

            else:
                logging.debug(f"[{symbol}] 无法精确匹配时间戳: {timestamp}")



        # 记录对齐结果
        original_count1 = len(ohlcv1)
        original_count2 = len(ohlcv2)
        aligned_count = len(aligned_data)

        logging.info(f"[{symbol}] 数据对齐完成: 原始数据({original_count1}, {original_count2}) -> 对齐数据({aligned_count})")


        if aligned_count < min(original_count1, original_count2) * 0.8:
            logging.warning(f"[{symbol}] 对齐后数据量显著减少，可能存在时间同步问题")

        return aligned_data

    async def _fetch_ohlcv_in_batches(self, exchange, symbol: str, timeframe: str, since: int):
        """分批获取OHLCV数据，突破1000条限制"""
        all_ohlcv = []
        current_since = since
        batch_size = 100
        max_batches = 500  # 最多获取500批，避免无限循环
        batch_count = 0

        # 计算时间间隔（毫秒）
        timeframe_ms = self._get_timeframe_ms(timeframe)

        try:
            while batch_count < max_batches:
                batch_count += 1

                # 获取一批数据
                batch_data = await exchange.fetch_ohlcv(
                    symbol,
                    timeframe,
                    current_since,
                    limit=batch_size
                )

                if not batch_data:
                    logging.info(f"[{exchange.id}] {symbol} 第{batch_count}批: 无数据，停止获取")
                    break

                # 添加到总数据中
                all_ohlcv.extend(batch_data)

                logging.info(f"[{exchange.id}] {symbol} 第{batch_count}批: 获取{len(batch_data)}条数据")

                # 如果获取的数据少于批次大小，说明已经获取完所有数据
                if len(batch_data) < batch_size:
                    logging.debug(f"[{exchange.id}] {symbol} 数据获取完成，共{len(all_ohlcv)}条")
                    break

                # 更新下次获取的起始时间（使用最后一条数据的时间戳+1个时间间隔）
                last_timestamp = batch_data[-1][0]
                current_since = last_timestamp + timeframe_ms


            if batch_count >= max_batches:
                logging.warning(f"[{exchange.id}] {symbol} 达到最大批次限制({max_batches})，可能数据不完整")

            logging.info(f"[{exchange.id}] {symbol} 总共获取{len(all_ohlcv)}条历史数据")
            return all_ohlcv

        except Exception as e:
            logging.error(f"[{exchange.id}] {symbol} 分批获取数据失败: {type(e).__name__}: {str(e)}")
            return all_ohlcv  # 返回已获取的数据

    def _get_timeframe_ms(self, timeframe: str) -> int:
        """获取时间间隔的毫秒数"""
        timeframe_map = {
            '1m': 60 * 1000,
            '3m': 3 * 60 * 1000,
            '5m': 5 * 60 * 1000,
            '15m': 15 * 60 * 1000,
            '30m': 30 * 60 * 1000,
            '1h': 60 * 60 * 1000,
            '2h': 2 * 60 * 60 * 1000,
            '4h': 4 * 60 * 60 * 1000,
            '6h': 6 * 60 * 60 * 1000,
            '8h': 8 * 60 * 60 * 1000,
            '12h': 12 * 60 * 60 * 1000,
            '1d': 24 * 60 * 60 * 1000,
            '3d': 3 * 24 * 60 * 60 * 1000,
            '1w': 7 * 24 * 60 * 60 * 1000,
        }
        return timeframe_map.get(timeframe, 60 * 1000)  # 默认1分钟

    def _deduplicate_data(self, data_list):
        """去重并按时间戳排序"""
        if not data_list:
            return []

        # 按时间戳去重
        seen_timestamps = set()
        unique_data = []

        for data in data_list:
            timestamp = data['timestamp']
            if timestamp not in seen_timestamps:
                seen_timestamps.add(timestamp)
                unique_data.append(data)

        # 按时间戳排序
        unique_data.sort(key=lambda x: x['timestamp'])

        return unique_data
    def _analyze_symbols(self) -> List[Dict]:
        """分析所有币种的价差波动，返回排序后的结果"""
        logging.info("开始分析币种价差波动...")

        analysis_results = []

        for symbol in self.price_data:
            try:
                result = self._analyze_single_symbol(symbol)
                if result:
                    analysis_results.append(result)
            except Exception as e:
                logging.warning(f"分析币种 {symbol} 失败: {str(e)}")
                continue

        # 按价差波动率排序
        analysis_results.sort(key=lambda x: x['volatility_score'], reverse=True)

        # 保存分析结果
        self.analysis_results = {result['symbol']: result for result in analysis_results}
        self._save_price_and_analysis_data()

        logging.info(f"分析完成，共分析 {len(analysis_results)} 个币种")
        return analysis_results

    def _analyze_single_symbol(self, symbol: str) -> Optional[Dict]:
        """分析单个币种的价差数据"""
        data = self.price_data.get(symbol, [])

        if len(data) < 10:  # 至少需要10个数据点
            return None

        # 提取价差数据
        spreads = [point['spread'] for point in data]
        timestamps = [datetime.fromisoformat(point['timestamp']) for point in data]

        # 计算统计指标
        mean_spread = np.mean(spreads)
        std_spread = np.std(spreads)
        max_spread = np.max(spreads)
        min_spread = np.min(spreads)

        # 计算价差超过阈值的频率
        threshold_01 = sum(1 for s in spreads if s > 0.1) / len(spreads)
        threshold_02 = sum(1 for s in spreads if s > 0.2) / len(spreads)
        threshold_05 = sum(1 for s in spreads if s > 0.5) / len(spreads)

        # 计算波动率评分（综合考虑均值、标准差、最大值和频率）
        volatility_score = (
            mean_spread * 0.3 +           # 平均价差权重30%
            std_spread * 0.3 +            # 标准差权重30%
            max_spread * 0.2 +            # 最大价差权重20%
            threshold_02 * 100 * 0.2      # 超过0.2%频率权重20%
        )

        # 计算数据质量评分
        data_quality = min(1.0, len(data) / 100)  # 数据点越多质量越高

        # 获取最新价格信息
        latest_data = data[-1]

        return {
            'symbol': symbol,
            'data_points': len(data),
            'mean_spread': round(mean_spread, 4),
            'std_spread': round(std_spread, 4),
            'max_spread': round(max_spread, 4),
            'min_spread': round(min_spread, 4),
            'threshold_01_freq': round(threshold_01, 4),
            'threshold_02_freq': round(threshold_02, 4),
            'threshold_05_freq': round(threshold_05, 4),
            'volatility_score': round(volatility_score, 4),
            'data_quality': round(data_quality, 4),
            'latest_spread': round(latest_data['spread'], 4),
            'latest_timestamp': latest_data['timestamp'],
            'analysis_timestamp': datetime.now().isoformat()
        }

    def _get_top_symbols(self, count: int = 20) -> List[str]:
        """获取价差波动最大的前N个币种"""
        if not self.analysis_results:
            logging.warning("没有分析结果，请先运行 analyze_symbols()")
            return []

        # 按波动率评分排序
        sorted_symbols = sorted(
            self.analysis_results.items(),
            key=lambda x: x[1]['volatility_score'],
            reverse=True
        )

        # 过滤数据质量较差的币种
        filtered_symbols = [
            (symbol, data) for symbol, data in sorted_symbols
            if data['data_quality'] > 0.3 and data['data_points'] >= 20
        ]

        top_symbols = [symbol for symbol, _ in filtered_symbols[:count]]

        logging.info(f"选出前 {len(top_symbols)} 个优质币种: {top_symbols}")
        return top_symbols

    def _print_analysis_report(self, top_count: int = 20):
        """打印分析报告"""
        if not self.analysis_results:
            logging.warning("没有分析结果")
            return

        logging.info("\n" + "="*80)
        logging.info("币种价差分析报告")
        logging.info("="*80)

        sorted_results = sorted(
            self.analysis_results.items(),
            key=lambda x: x[1]['volatility_score'],
            reverse=True
        )

        logging.info(f"{'排名':<4} {'币种':<15} {'波动评分':<10} {'平均价差%':<10} {'最大价差%':<10} {'最小价差%':<10} {'数据点':<8}")
        logging.info("-" * 80)

        for i, (symbol, data) in enumerate(sorted_results[:top_count], 1):
            logging.info(f"{i:<4} {symbol:<20} {data['volatility_score']:<15.4f} "
                  f"{data['mean_spread']:<13.4f} {data['max_spread']:<13.4f} "
                  f"{data['min_spread']:<13.4f} {data['data_points']:<8}")

        logging.info("="*80)



    async def close(self):
        """关闭分析器"""
        try:
            self._save_price_and_analysis_data()
            await self.ex1.close()
            await self.ex2.close()
            logging.info("分析器已关闭")
        except Exception as e:
            logging.error(f"关闭分析器时出错: {str(e)}")
