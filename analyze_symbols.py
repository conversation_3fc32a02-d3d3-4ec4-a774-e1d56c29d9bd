#!/usr/bin/env python3
"""
币种分析工具 - 简化版
使用工具模块进行交易所实例化
"""

import asyncio
import logging
import argparse
import sys
from datetime import datetime, timezone, timedelta
from strategy.symbol_analyzer import SymbolAnalyzer
from utils import create_exchange_pair_safe


async def main():
    """币种分析工具 - 快速分析和查看币种价差数据"""

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='币种价差历史数据分析工具')
    parser.add_argument('--history-hours', type=int, default=24,
                        help='历史数据回溯时长（小时），默认24小时')
    parser.add_argument('--top', type=int, default=20,
                        help='显示前N个币种，默认20个')

    args = parser.parse_args()


    # 创建交易所实例
    exchange1, exchange2=create_exchange_pair_safe(exchange1_name='bitget', exchange2_name='okx')
    if not exchange1 or not exchange2:
        print("❌ 交易所实例创建失败")
        return

    # 创建分析器
    print("📊 创建币种分析器...")
    analyzer = SymbolAnalyzer(
        exchange1=exchange1,
        exchange2=exchange2,
        analysis_duration_hours=args.history_hours,
        top_symbols_count=args.top
    )

    try:
        await analyzer.run()

    except Exception as e:
        print(f"程序异常: {type(e).__name__}: {str(e)}")

    finally:
        await analyzer.close()


if __name__ == '__main__':
    # 配置日志，使用 UTC+8 时区，包含函数名
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] [%(funcName)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[logging.StreamHandler(sys.stdout)],
        force=True
    )
    logging.Formatter.converter = lambda *args: datetime.now(timezone(timedelta(hours=8))).timetuple()
    
    # 运行主程序
    asyncio.run(main())
